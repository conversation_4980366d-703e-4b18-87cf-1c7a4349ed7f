import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts';
import { apiClient } from '../api';
import { Button } from '../components/ui/button';
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from '../components/ui/pagination';

interface AdminReferralCredit {
  id: number;
  amount: number;
  credit_type: string;
  expires_at: string | null;
  used_amount: number;
  remaining_amount: number;
  is_active: boolean;
  is_expired: boolean;
  is_usable: boolean;
  created_at: string;
  ip_address: string | null;
  user_id: number;
  user_email: string;
  user_username: string;
  referral_user_id: number | null;
  referral_user_email: string | null;
  referral_user_username: string | null;
}

interface PaginatedReferralCredits {
  credits: AdminReferralCredit[];
  total_pages: number;
  current_page: number;
  has_next: boolean;
  has_previous: boolean;
}

const AdminReferrals: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [credits, setCredits] = useState<AdminReferralCredit[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [paginationInfo, setPaginationInfo] = useState({
    total_pages: 1,
    current_page: 1,
    has_next: false,
    has_previous: false
  });
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [editingCredit, setEditingCredit] = useState<number | null>(null);
  const [editAmount, setEditAmount] = useState<string>('');

  const perPage = 50;

  // Redirect if not admin
  useEffect(() => {
    if (!isAuthenticated || !user?.is_admin) {
  window.location.href = '/dashboard';
    }
  }, [isAuthenticated, user]);

  // Load credits when component mounts or dependencies change
  useEffect(() => {
    if (!user?.is_admin) {
      return;
    }
    loadCredits();
  }, [user, currentPage, sortBy, sortOrder]);

  const loadCredits = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiClient.getAdminReferralCredits(currentPage, perPage, sortBy, sortOrder);

      setCredits(response.credits);
      setPaginationInfo({
        total_pages: response.total_pages,
        current_page: response.current_page,
        has_next: response.has_next,
        has_previous: response.has_previous
      });
    } catch (e: any) {
      setError(e?.message || 'Failed to load referral credits');
    } finally {
      setLoading(false);
    }
  };

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const startEdit = (credit: AdminReferralCredit) => {
    setEditingCredit(credit.id);
    setEditAmount(credit.amount.toString());
  };

  const cancelEdit = () => {
    setEditingCredit(null);
    setEditAmount('');
  };

  const saveEdit = async (creditId: number) => {
    try {
      const amount = parseFloat(editAmount);
      if (isNaN(amount) || amount < 0) {
        setError('Please enter a valid amount');
        return;
      }

      await apiClient.adjustReferralCredit(creditId, amount);
      setEditingCredit(null);
      setEditAmount('');
      await loadCredits(); // Reload data
    } catch (e: any) {
      setError(e?.message || 'Failed to adjust credit');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSortIcon = (field: string) => {
    if (sortBy !== field) return '↕️';
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  if (!user?.is_admin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
          <p className="text-muted-foreground mt-2">You must be an administrator to view this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Referral Credits Management</h1>
        <p className="text-muted-foreground mt-2">
          Monitor and manage referral credits. Sort by IP address to detect potential abuse.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="rounded border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-muted">
              <tr>
                <th 
                  className="text-left p-3 cursor-pointer hover:bg-muted/80"
                  onClick={() => handleSort('created_at')}
                >
                  Date {getSortIcon('created_at')}
                </th>
                <th 
                  className="text-left p-3 cursor-pointer hover:bg-muted/80"
                  onClick={() => handleSort('user_email')}
                >
                  User {getSortIcon('user_email')}
                </th>
                <th className="text-left p-3">Type</th>
                <th 
                  className="text-left p-3 cursor-pointer hover:bg-muted/80"
                  onClick={() => handleSort('amount')}
                >
                  Amount {getSortIcon('amount')}
                </th>
                <th className="text-left p-3">Used</th>
                <th className="text-left p-3">Remaining</th>
                <th 
                  className="text-left p-3 cursor-pointer hover:bg-muted/80"
                  onClick={() => handleSort('ip_address')}
                >
                  IP Address {getSortIcon('ip_address')}
                </th>
                <th className="text-left p-3">Referral Partner</th>
                <th className="text-left p-3">Status</th>
                <th className="text-left p-3">Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={10} className="text-center p-8 text-muted-foreground">
                    Loading...
                  </td>
                </tr>
              ) : credits.length === 0 ? (
                <tr>
                  <td colSpan={10} className="text-center p-8 text-muted-foreground">
                    No referral credits found.
                  </td>
                </tr>
              ) : (
                credits.map((credit) => (
                  <tr key={credit.id} className="border-t hover:bg-muted/20">
                    <td className="p-3 text-sm">
                      {formatDate(credit.created_at)}
                    </td>
                    <td className="p-3 text-sm">
                      <div>
                        <div className="font-medium">{credit.user_username}</div>
                        <div className="text-muted-foreground text-xs">{credit.user_email}</div>
                      </div>
                    </td>
                    <td className="p-3 text-sm">
                      <span className={`px-2 py-1 rounded text-xs ${
                        credit.credit_type === 'referrer' 
                          ? 'bg-blue-100 text-blue-800' 
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {credit.credit_type}
                      </span>
                    </td>
                    <td className="p-3 text-sm">
                      {editingCredit === credit.id ? (
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            value={editAmount}
                            onChange={(e) => setEditAmount(e.target.value)}
                            className="w-20 px-2 py-1 border rounded text-sm"
                          />
                          <Button
                            size="sm"
                            onClick={() => saveEdit(credit.id)}
                            className="px-2 py-1 text-xs"
                          >
                            Save
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={cancelEdit}
                            className="px-2 py-1 text-xs"
                          >
                            Cancel
                          </Button>
                        </div>
                      ) : (
                        <span className="font-medium">${credit.amount.toFixed(2)}</span>
                      )}
                    </td>
                    <td className="p-3 text-sm">${credit.used_amount.toFixed(2)}</td>
                    <td className="p-3 text-sm">${credit.remaining_amount.toFixed(2)}</td>
                    <td className="p-3 text-sm font-mono">
                      {credit.ip_address || 'N/A'}
                    </td>
                    <td className="p-3 text-sm">
                      {credit.referral_user_username ? (
                        <div>
                          <div className="font-medium">{credit.referral_user_username}</div>
                          <div className="text-muted-foreground text-xs">{credit.referral_user_email}</div>
                        </div>
                      ) : (
                        'N/A'
                      )}
                    </td>
                    <td className="p-3 text-sm">
                      <span className={`px-2 py-1 rounded text-xs ${
                        credit.is_usable 
                          ? 'bg-green-100 text-green-800' 
                          : credit.is_expired 
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {credit.is_usable ? 'Active' : credit.is_expired ? 'Expired' : 'Inactive'}
                      </span>
                    </td>
                    <td className="p-3 text-sm">
                      {editingCredit !== credit.id && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => startEdit(credit)}
                          className="px-2 py-1 text-xs"
                        >
                          Adjust
                        </Button>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {paginationInfo.total_pages > 1 && (
          <div className="p-4 border-t flex justify-center">
            <Pagination>
              <PaginationContent>
                {paginationInfo.has_previous && (
                  <PaginationItem>
                    <PaginationPrevious 
                      onClick={() => handlePageChange(currentPage - 1)}
                      className="cursor-pointer"
                    />
                  </PaginationItem>
                )}
                
                {Array.from({ length: Math.min(5, paginationInfo.total_pages) }, (_, i) => {
                  let pageNum: number;
                  if (paginationInfo.total_pages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= paginationInfo.total_pages - 2) {
                    pageNum = paginationInfo.total_pages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <PaginationItem key={pageNum}>
                      <PaginationLink
                        onClick={() => handlePageChange(pageNum)}
                        isActive={pageNum === currentPage}
                        className="cursor-pointer"
                      >
                        {pageNum}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}
                
                {paginationInfo.has_next && (
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => handlePageChange(currentPage + 1)}
                      className="cursor-pointer"
                    />
                  </PaginationItem>
                )}
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminReferrals;
