import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts';
import { Button } from '@/components/ui/button';

const Home: React.FC = () => {
  const { isAuthenticated, user } = useAuth();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen text-center">

      <script src="https://fast.wistia.com/player.js" async></script><script src="https://fast.wistia.com/embed/3tab7gg6go.js" async type="module"></script><style>wistia-player[media-id='3tab7gg6go']:not(:defined) { background: center / contain no-repeat url('https://fast.wistia.com/embed/medias/3tab7gg6go/swatch'); display: block; filter: blur(5px); padding-top:56.25%; }</style> <wistia-player media-id="3tab7gg6go" aspect="1.7777777777777777"></wistia-player>

      <h1 className="text-4xl font-bold tracking-tight lg:text-5xl mb-2">
        Manidae Cloud
      </h1>
      <p className="text-xl text-muted-foreground mb-8 max-w-2xl">
        Simplified managed hosting platform for deploying pre-configured open-source software packages on cloud infrastructure.
      </p>

      {isAuthenticated ? (
        <div className="space-y-4">
          <p className="text-lg">Welcome back, {user?.username}!</p>
          <div className="space-x-4">
            <Link to="/dashboard">
              <Button>Go to Dashboard</Button>
            </Link>
            <Link to="/create-deployment">
              <Button variant="secondary">Create Deployment</Button>
            </Link>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="space-x-4">
            <Link to="/login">
              <Button>Login</Button>
            </Link>
            <Link to="/signup">
              <Button variant="secondary">Sign Up</Button>
            </Link>
          </div>
          <Link to="/pricing">
            <Button variant="outline">View Pricing</Button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default Home;
